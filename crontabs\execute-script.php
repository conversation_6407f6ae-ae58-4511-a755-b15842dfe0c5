<?php
	/** \file execute-script.php
	 *	\ingroup crontabs
	 *
	 * 	Ce script est destiné à lancer tous les scripts se trouvant dans /crontabs.
	 * 	Paramètre obligatoire :
	 * 		- --script : nom du script (hors extension)
	 *
	 * 	Optionnel :
	 * 		- --mode   : 0 = chaque tenant ou 1 = chaque site de chaque tenant (ex. reconstruire le sitemap pour tous les site d'un tenant)
	 * 		- --tnt_id : identifiant du tenant
	 * 		- --other  : paramètres propre au script demandé, ex : --other="opt1=1&opt2=2&opt3=3"
	 *
	 * 	Exemples de lancement :
	 * 		php execute-script.php --script [NOM DU SCRIPT]
	 * 		php execute-script.php --script [NOM DU SCRIPT] --mode [0|1]
	 * 		php execute-script.php --script [NOM DU SCRIPT] --mode [0|1] --tnt_id [TNT_ID]
	 */

	
	set_include_path(dirname(__FILE__).'/../include');
	require_once('define.inc.php');
	require_once('strings.inc.php');
	require_once('RegisterGCP.inc.php');
	require_once('cfg.variables.inc.php');
 
	{ // Contrôle des paramètres obligatoire
		// Vérifie que tous les paramètres on été donnés
		$ar_params = getopt('h', array('script:', 'mode:', 'other::'));

		if( !ria_array_key_exists(array('script'), $ar_params) ){
			$ar_params = array('h'=>true);
		}

		if( array_key_exists('h', $ar_params) ){
			print '|---------------------------------------------------------------------------------------------|'.PHP_EOL;
			print '|    Ce script est destiné à lancer tous les scripts se trouvant dans /crontabs.              |'.PHP_EOL;
			print '|---------------------------------------------------------------------------------------------|'.PHP_EOL;
			print '|    Paramètre obligatoire :                                                                  |'.PHP_EOL;
			print '|         --script : nom du script (hors extension)                                           |'.PHP_EOL;
			print '|---------------------------------------------------------------------------------------------|'.PHP_EOL;
			print '|    Optionnel :                                                                              |'.PHP_EOL;
			print '|         --mode   : 0 = chaque tenant ou 1 = chaque site de chaque tenant                    |'.PHP_EOL;
			print '|         --other  : paramètres propre au script demandé, ex : --other="opt1=1&opt2=2&opt3=3" |'.PHP_EOL;
			print '|---------------------------------------------------------------------------------------------|'.PHP_EOL;
			print '|    Exemples de lancement :                                                                  |'.PHP_EOL;
			print '|        php execute-script.php --script [NOM DU SCRIPT]                                      |'.PHP_EOL;
			print '|        php execute-script.php --script [NOM DU SCRIPT] --mode [0|1]                         |'.PHP_EOL;
			print '|        php execute-script.php --script [NOM DU SCRIPT] --mode [0|1]       					 |'.PHP_EOL;
			print '|---------------------------------------------------------------------------------------------|'.PHP_EOL;
			exit;
		}

		// Défini un mode de lancement par défaut
		// Il s'agit du plus commun, celui cross-tenant (mode = 0)
		// Sachant que l'autre est cross-website (mode = 1)
		if( !array_key_exists('mode', $ar_params) ){
			$ar_params['mode'] = 0;
		}

		if( array_key_exists('other', $ar_params) ){
			if( trim($ar_params['other']) != '' ){
				parse_str( $ar_params['other'], $other );

				foreach( $other as $key => $value ){
					if( $value === 'false' ){
						$other[ $key ] = false;
					}
				}

				$ar_params = array_merge( $other, $ar_params );
			}

			unset( $ar_params['other'] );
		}

		// Vérifie que le script existe
		$script = dirname(__FILE__).'/'.$ar_params['script'].'.php';
		if( !file_exists($script) ){
			error_log('Le script '.$ar_params['script'].'.php'.' n\'éxiste pas');
			exit;
		}
	}

	$ar_params['tnt_id'] = getenv('ENVRIA_TNT_ID');

	{ // Empêche le lancement simultané du même script
		

		$log_file = "/apps/instances/73929.yuto_devu0001/www/lock_check_".$ar_params['script'].".log";
		$lock = dirname(__FILE__).'/../locks/lock-'.urlalias($ar_params['script']).'-'.$ar_params['tnt_id'].'.txt';
		error_log(date("Y-m-d H:i:s") . " ********************************** " . PHP_EOL,3,$log_file);
		
		if( file_exists($lock) )
		{
			error_log(date("Y-m-d H:i:s") . " - <".__FILE__.__LINE__."> Lancement du cron, un lock est existant : " .$ar_params['script'] . PHP_EOL,3,$log_file);

			$lock_datum = json_decode(file_get_contents($lock), true);
			error_log(date("Y-m-d H:i:s") . " - <".__FILE__.__LINE__."> Contenu du lock : " .print_r($lock_datum,true) . PHP_EOL,3,$log_file);

			if(!empty($lock_datum) && isset($lock_datum['date_execution'],$lock_datum['nbre_relance'],$lock_datum['delai_verif']))
			{
				$currentTime = time();
				$lastExecution = strtotime($lock_datum['date_execution']);
				$delayInSeconds = $lock_datum['delai_verif'] * 3600;
				$nb_relance = $lock_datum['nbre_relance'];
				$max_relance = $lock_datum['relance_max'];
				
				if($currentTime - $lastExecution > $delayInSeconds )
				{
					if($nb_relance >= $max_relance) {
						if ($nb_relance == 3 || $nb_relance == 6 || ($nb_relance >= 12 && $nb_relance % 12 == 0)) {
							$tnt_name = tnt_tenants_get_name($ar_params['tnt_id']);

							$to = '<EMAIL>';//'<EMAIL>';
							$subject = sprintf('Alerte cron : %s  -  #%d %s', $ar_params['script'],$ar_params['tnt_id'] ,$tnt_name);
							
							$message = sprintf('Le cron "%s" est en erreur après %d occurrences pour le client #%d %s', $ar_params['script'], $lock_datum['nbre_relance'], $ar_params['tnt_id'], $tnt_name);
								
							mail($to, $subject, $message);
							error_log(date("Y-m-d H:i:s") . " - <".__FILE__.__LINE__."> Mail : " .$message . PHP_EOL,3,$log_file);

						}
						error_log(date("Y-m-d H:i:s") . " Cron non relancé : nombre maximum de relances atteint ". PHP_EOL,3,$log_file);

						error_log('Cron non relancé : nombre maximum de relances atteint pour "' . $ar_params['script'] . '".');
						$lock_datum['nbre_relance'] += 1;
						error_log(date("Y-m-d H:i:s") ." - <" . __FILE__.__LINE__ ."> - On a atteint le nombre maximum de relances, on met le nombre de relances à jour : ". print_r($lock_datum,true). PHP_EOL,3,$log_file);
						file_put_contents($lock, json_encode($lock_datum,JSON_PRETTY_PRINT));
						return; 
					}
					$lock_datum['date_execution'] = date('Y-m-d H:i:s', $currentTime);
					$lock_datum['nbre_relance'] += 1;
					error_log(date("Y-m-d H:i:s") ." - <" . __FILE__.__LINE__ ."> - Nombre de relances max non atteint, on relance le cron, nouveau lock : ". print_r($lock_datum,true). PHP_EOL,3,$log_file);

					file_put_contents($lock, json_encode($lock_datum,JSON_PRETTY_PRINT));
				}	
				else 
				{
					error_log(date("Y-m-d H:i:s") . " - <".__FILE__.__LINE__."> - On ne rentre pas dans le délai de vérif : lancement simulatané !" . PHP_EOL,3,$log_file);
					error_log('Lancement simultané de "'.$ar_params['script'].'".');
					return;
				}
			}
			else
			{
				error_log(date("Y-m-d H:i:s") . " - <".__FILE__.__LINE__."> - Le lock n'a pas de datum valides : lancement simulatané !" . PHP_EOL,3,$log_file);
				error_log('Lancement simultané de "'.$ar_params['script'].'".');
				return;
			}

		}
		else
		{

			error_log(date("Y-m-d H:i:s") . " - <".__LINE__."> Lancement du cron : " .$ar_params['script'] . PHP_EOL,3,$log_file);

			$lock_datum = [];
			// Je ne sais pas si c'est le fait de lancer à la mano mais les methodes comme cfg_overrides_get()  ou cfg_overrides_get_value() ne fonctionnent pas correctement
			// J'ai donc fait une requête SQL en attendant pour récupérer le délai de verif du cron
			// Mais on peut imaginer  : cfg_overrides_get(0,array(),'check_lock_cron') ou cfg_overrides_get_value('check_lock_cron')

		    $config_check_lock_cron = ria_mysql_query("SELECT ovr_value as value FROM cfg_overrides WHERE ovr_tnt_id = ". $ar_params['tnt_id'] ." AND ovr_var_code = 'check_lock_cron'");
			
			if( $config_check_lock_cron && ria_mysql_num_rows($config_check_lock_cron) ){
				$cfg = ria_mysql_fetch_assoc($config_check_lock_cron);
				
				$cfg_check_log_cron = json_decode($cfg['value'],true);
				if(!empty($cfg_check_log_cron)){
					if(isset($cfg_check_log_cron[$ar_params['script']])){
						$config_cron = $cfg_check_log_cron[$ar_params['script']];
					}
					else{
						$config_cron = $cfg_check_log_cron['defaut'];
					}
				}
				$delai = $config_cron['delai_cron'];
				error_log(date("Y-m-d H:i:s") . " - Config du cron  : " .print_r($config_cron,true) . PHP_EOL,3,"/apps/instances/73929.yuto_devu0001/www/crontest.log");

				$relance_max = !isset($config_cron['relance_max']) ? $cfg_check_log_cron['defaut']['relance_max'] : $config_cron['relance_max'];
				
				// création du json pour le lock
				$lock_datum = [
					'date_execution' => date('Y-m-d H:i:s'),
					'nbre_relance'   => 0,                  
					'delai_verif'    => $delai,
					'relance_max' => $relance_max
				];
			}
			error_log(date("Y-m-d H:i:s") . " - <".__LINE__."> Lock créé : " . json_encode($lock_datum, JSON_PRETTY_PRINT). PHP_EOL,3,$log_file);

			// Création du fichier de lock
			$fp = fopen($lock, 'w+');
			if ($fp) {
				fwrite($fp, json_encode($lock_datum, JSON_PRETTY_PRINT));
				fclose($fp);
			} else {
				error_log('Impossible de créer le fichier de lock pour "'.$ar_params['script'].'".');
			}
		}
	}

	try{
		// Charge la ou les connexions aux serveurs SQL selon si un tenant a été passé en paramètre
		if( RegisterGCP::onGcloud() ){
			$ar_server = RegisterGCP::getConnections( $ar_params['tnt_id'] );
		}
		else
		{
			$ar_server = [
				md5('localhostriashop') => [
					'mysql_server' => getenv('ENVRIA_BDD_SERVER'),
					'mysql_base' => getenv('ENVRIA_BDD_NAME'),
					'mysql_user' => getenv('ENVRIA_BDD_LOGIN'),
					'mysql_password' => getenv('ENVRIA_BDD_PWD')
				]
			];
		}

		foreach( $ar_server as $one_server ){
			$start = microtime(true);

			// Réalise la connexion au serveur
			// Cette partie fonction aussi bien sur la GCP qu'en dehors car $one_server contient les informations
			// de connexion (base, utilisateur, mot de passer et serveur)
			if( !RegisterGCPConnection::connect($one_server) ){
				// La connexion a échouée, on ne vas pas plus loin
				error_log('Echec de la connexion MySQL '.$one_server);
				continue;
			}

			if( $ar_params['tnt_id'] > 0 ){
				$ar_tnt_ids = [ (int) $ar_params['tnt_id'] ];
			}else{
				if( RegisterGCP::onGcloud() ){
					$ar_tnt_ids = RegisterGCP::getTenantIDs( $one_server['mysql_server'] );
				}else{
					// Hors GCP, les tenants sont chargés à partie de la base de données
					$r_tnt = tnt_tenants_get();
					if( $r_tnt ){
						while( $tnt = ria_mysql_fetch_assoc($r_tnt) ){
							$ar_tnt_ids[] = $tnt['id'];
						}
					}
				}
			}

			// Retire le tenant 0 du résultat
			$key_zero = array_search( 0, $ar_tnt_ids, true );
			if( $key_zero !== false ){
				unset( $ar_tnt_ids[$key_zero] );
			}

			// Contrôle pour les tenants que les scripts sont bien activés
			// Seule sur la GCP, cette info n'existe pas en dehors
			if( RegisterGCP::onGcloud() ){
				foreach( $ar_tnt_ids as $key=>$tnt_id ){
					$Monitoring = new Monitoring();
					$Monitoring->reloadCronRules();

					// Si le script n'est pas activé alors il ne sera pas lancé pour ce tenant
					if( !$Monitoring->cronIsActive($tnt_id, $ar_params['script']) ){
						unset( $ar_tnt_ids[$key] );
					}
				}
			}

			// S'il ne reste aucun tenant, on ne va pas plus loin
			if( count($ar_tnt_ids) == 0 ){
				continue;
			}

			// Charge le tableau des configs selon le mode d'exécution
			if( $ar_params['mode'] == '1' ){
				$configs = cfg_variables_get_all_websites( $ar_tnt_ids );
			}elseif( $ar_params['mode'] == '0' ){
				$configs = cfg_variables_get_all_tenants( $ar_tnt_ids );
			}

			// Echec lors du chargement de la configuration, on ne va pas plus loin
			if( !isset($configs) || !is_array($configs) ){
				error_log( 'execute-script.php: Echec lors du chargement de la configuration : '.$one_server['mysql_server']);
				continue;
			}

			// Recherche dans le script un foreach( $configs as $config )
			// S'il n'est pas trouvé, l'exécution se chargera de le faire à sa place
			$content_script = file_get_contents( $script );
			$content_script = str_replace([' ', '	'], '', $content_script);
			$foreach_check = preg_match( '/(foreach\(\$configsas\$config\))/i', $content_script );

			// Lancement du script pour toutes les configs en même temps
			if( $foreach_check ){
				try{
					include( $script );
				}catch( Exception $e ){
					error_log( 'execute-script.php:'.$ar_params['script'].':'.$ar_params['tnt_id'].' => '.$e->getMessage() );
				}
			}else{
				foreach( $configs as $config ){
					try{
						include( $script );
					}catch( Exception $e ){
						error_log( 'execute-script.php:'.$ar_params['script'].':'.$ar_params['tnt_id'].' => '.$e->getMessage() );
					}
				}
			}

			// Le lancement par serveur sera logger avec les infos de lancement ainsi que le temps d'exécution
			$ar_exec = array(
				'tenant' => $ar_tnt_ids,
				'server' => $one_server,
				'date' => date('Y-m-d H:i:s'),
				'script' => $ar_params['script'],
				'time' => round(microtime(true) - $start, 4),
				'label_time' => ria_seconds_to_time(round(microtime(true) - $start, 4), 4)
			);

			$log_exec = json_encode($ar_exec);
			error_log($log_exec.PHP_EOL, 3, '/var/log/php/log-execute-script.log');
		}
	}catch( Exception $e ){
		error_log( 'Exception sur execute-script : '.print_r($e, true) );
	}

	{ // Retire le lock après exécution du script
		/*if( !unlink($lock) ){
			error_log('Impossible de supprimer le fichier temporaire "'.$lock.'".');
		}*/
	}